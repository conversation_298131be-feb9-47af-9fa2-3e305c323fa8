# 知识库管理系统功能完善计划

## 📋 老架构 vs 新架构功能比对清单

### 🔍 已识别的功能差异

#### 1. 管理员登录功能 ❌ 新架构缺失
**老架构 (media/index.html)**：
- ✅ 完整的登录/退出功能
- ✅ 登录对话框界面
- ✅ Token管理和本地存储
- ✅ 基于登录状态的权限控制（审核、删除按钮显示）
- ✅ 登录后可查看更多管理功能

**新架构 (templates/)**：
- ❌ 完全没有登录功能
- ❌ 没有权限控制
- ❌ 所有功能都是公开访问

#### 2. 文件预览功能 ❌ 新架构缺失
**老架构 (media/index.html)**：
- ✅ 点击"预览"按钮查看文件详细内容
- ✅ 预览抽屉（Drawer）显示文件的Q&A数据
- ✅ 预览模态框编辑具体的Q&A内容
- ✅ 支持Markdown渲染
- ✅ 可以删除预览中的单条数据
- ✅ 可以编辑和更新Q&A内容
- ✅ 支持索引数据的查看和编辑

**新架构 (templates/docs.html)**：
- ❌ 只有一个空的 `viewFile()` 方法
- ❌ 没有预览界面
- ❌ 无法查看文件内容

#### 3. 文件审核功能 ⚠️ 新架构部分缺失
**老架构 (media/index.html)**：
- ✅ 审核对话框
- ✅ 审核表单（审核意见、状态选择）
- ✅ 基于登录状态的审核权限控制

**新架构 (templates/docs.html)**：
- ❌ 没有审核功能界面
- ❌ 没有审核相关的前端代码

#### 4. 数据统计和筛选 ⚠️ 新架构部分实现
**老架构 (media/index.html)**：
- ✅ 基于登录状态的数据筛选
- ✅ 审核状态筛选

**新架构 (templates/docs.html)**：
- ✅ 基本的统计显示
- ❌ 缺少基于权限的筛选

## 🎯 修改计划

### 阶段一：核心功能补全

#### 任务1：添加管理员登录功能
- **目标文件**：`templates/docs.html`, `templates/home.html`
- **功能要求**：
  - 添加登录/退出按钮到页面头部
  - 创建登录对话框组件
  - 实现Token管理和本地存储
  - 添加权限控制逻辑
  - 基于登录状态显示/隐藏功能按钮
- **后端接口**：已存在 `/login/` 接口

#### 任务2：实现文件预览功能
- **目标文件**：`templates/docs.html`
- **功能要求**：
  - 在文件列表中添加预览按钮
  - 创建预览抽屉组件（Element UI Drawer）
  - 实现Q&A数据展示
  - 添加Markdown渲染支持（引入marked.js）
  - 实现预览模态框编辑功能
  - 支持单条数据删除
  - 支持内容编辑和更新
- **后端接口**：已存在 `/getDatasetdatas/` 接口

#### 任务3：添加文件审核功能
- **目标文件**：`templates/docs.html`
- **功能要求**：
  - 创建审核对话框
  - 添加审核表单（审核意见、状态选择）
  - 实现审核状态管理
  - 基于权限控制审核功能显示
- **后端接口**：已存在 `/auditCollection/` 接口

### 阶段二：界面优化和完善

#### 任务4：统一样式和交互
- 确保新功能与现有设计风格一致
- 优化响应式布局
- 添加加载状态和错误处理
- 使用统一的CSS变量

#### 任务5：权限控制完善
- 实现基于登录状态的功能显示
- 添加权限验证
- 优化用户体验

### 阶段三：测试和优化

#### 任务6：功能测试
- 测试登录/退出流程
- 测试预览功能的完整性
- 测试权限控制的正确性
- 测试审核功能

#### 任务7：性能优化
- 优化数据加载
- 添加缓存机制
- 优化用户交互体验

## 📝 具体实施步骤

### 步骤1：登录功能实现
1. 在 `templates/docs.html` 中添加登录按钮到工具栏
2. 创建登录对话框组件
3. 实现登录逻辑和Token管理
4. 添加权限控制到现有功能
5. 在 `templates/home.html` 中同步添加登录功能

### 步骤2：预览功能实现
1. 在文件列表操作列中添加预览按钮
2. 创建预览抽屉组件
3. 实现数据获取和展示逻辑
4. 添加Markdown渲染支持
5. 实现预览模态框编辑功能
6. 添加删除和更新功能

### 步骤3：审核功能实现
1. 添加审核按钮（基于权限显示）
2. 创建审核对话框
3. 实现审核表单和逻辑
4. 集成审核状态管理

### 步骤4：样式统一和优化
1. 确保所有新组件使用统一的CSS变量
2. 优化响应式布局
3. 添加动画效果和交互反馈
4. 完善错误处理和用户提示

## 🔧 技术实现要点

### 登录功能技术要点
- 使用localStorage存储token
- 实现权限状态管理
- 添加请求拦截器携带token
- 实现登录状态持久化

### 预览功能技术要点
- 使用Element UI的Drawer组件
- 集成marked.js进行Markdown渲染
- 实现数据的增删改查
- 优化大数据量的展示性能

### 审核功能技术要点
- 实现审核状态的可视化
- 添加审核历史记录
- 实现批量审核功能
- 优化审核流程的用户体验

## ✅ 完成标准

### 功能完成标准
- [ ] 登录功能完全可用，权限控制正确
- [ ] 预览功能完整，可以查看和编辑文件内容
- [ ] 审核功能可用，支持审核状态管理
- [ ] 所有功能与现有UI风格统一
- [ ] 响应式设计在各设备上正常工作

### 质量标准
- [ ] 代码符合项目规范
- [ ] 错误处理完善
- [ ] 用户体验流畅
- [ ] 性能表现良好
- [ ] 兼容性测试通过

---

**开始实施时间**：待确认
**预计完成时间**：分阶段完成
**负责人**：AI Assistant
**状态**：待开始
