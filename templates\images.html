<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片管理 - 知识库管理系统</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="/static/css/common.css">
    <!-- 引入Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- 引入Element UI组件库 -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- 引入axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        .images-header {
            background: var(--primary-gradient);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .images-header h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .images-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .back-btn {
            background: var(--background-white);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .filter-section {
            background: var(--background-white);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-light);
            margin-bottom: 20px;
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .images-table {
            background: var(--background-white);
            border-radius: 8px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-lighter);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .image-preview {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .image-preview:hover {
            transform: scale(1.1);
        }
        
        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-error {
            background: #fef0f0;
            color: var(--danger-color);
        }
        
        .status-normal {
            background: #f0f9ff;
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
        }
        
        .pagination-wrapper {
            padding: 20px;
            display: flex;
            justify-content: center;
            background: var(--background-white);
            border-top: 1px solid var(--border-lighter);
        }
        
        /* 图片预览对话框样式 */
        .image-dialog .el-dialog__body {
            text-align: center;
            padding: 20px;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 8px;
            box-shadow: var(--shadow-base);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table-header {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="images-header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h2>图片管理</h2>
                        <p>管理和修复知识库中的错误图片</p>
                    </div>
                    <a href="/" class="back-btn">
                        <i class="el-icon-arrow-left"></i>
                        返回首页
                    </a>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <el-select v-model="selectedDataset" placeholder="选择知识库" style="width: 200px;" @change="loadCollections">
                        <el-option 
                            v-for="dataset in datasets" 
                            :key="dataset.id" 
                            :label="dataset.name" 
                            :value="dataset.id">
                        </el-option>
                    </el-select>
                    
                    <el-select v-model="selectedCollection" placeholder="选择文档集合" style="width: 200px;" @change="loadImages">
                        <el-option
                            v-for="collection in collections"
                            :key="collection.id"
                            :label="collection.name"
                            :value="collection.id">
                        </el-option>
                    </el-select>

                    <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 240px;">
                    </el-date-picker>

                    <el-button @click="resetDateRange" size="small">
                        <i class="el-icon-refresh"></i>
                        重置时间
                    </el-button>

                    <el-button type="primary" @click="loadImages" :loading="loading">
                        <i class="el-icon-search"></i>
                        查询图片
                    </el-button>
                    
                    <el-button type="success" @click="batchUpdate" :disabled="!selectedImages.length">
                        <i class="el-icon-check"></i>
                        批量更新 ({{ selectedImages.length }})
                    </el-button>
                </div>
            </div>

            <!-- 图片列表 -->
            <div class="images-table">
                <div class="table-header">
                    <div class="table-title">图片列表 ({{ images.length }})</div>
                    <div>
                        <el-checkbox v-model="selectAll" @change="toggleSelectAll">全选</el-checkbox>
                    </div>
                </div>
                
                <el-table 
                    :data="paginatedImages" 
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    v-loading="loading">
                    
                    <el-table-column type="selection" width="55"></el-table-column>
                    
                    <el-table-column label="序号" width="80">
                        <template slot-scope="scope">
                            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="图片预览" width="120">
                        <template slot-scope="scope">
                            <img 
                                :src="scope.row.imageUrl" 
                                class="image-preview"
                                @click="previewImage(scope.row.imageUrl)"
                                @error="handleImageError"
                                alt="图片预览">
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="文档名称" min-width="200">
                        <template slot-scope="scope">
                            <div style="font-weight: 500;">{{ scope.row.documentName }}</div>
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                ID: {{ scope.row.documentId }}
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="图片ID" width="120">
                        <template slot-scope="scope">
                            <el-tag size="mini">{{ scope.row.imageId }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="状态" width="100">
                        <template slot-scope="scope">
                            <span :class="['status-tag', scope.row.hasError ? 'status-error' : 'status-normal']">
                                {{ scope.row.hasError ? '错误' : '正常' }}
                            </span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="更新时间" width="150">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.updateTime) }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="150">
                        <template slot-scope="scope">
                            <div class="action-buttons">
                                <el-button 
                                    size="mini" 
                                    type="primary" 
                                    @click="updateSingleImage(scope.row)"
                                    :loading="scope.row.updating">
                                    更新
                                </el-button>
                                <el-button 
                                    size="mini" 
                                    type="danger" 
                                    @click="deleteImage(scope.row)">
                                    删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="images.length">
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 图片预览对话框 -->
        <el-dialog 
            title="图片预览" 
            :visible.sync="previewDialogVisible" 
            width="60%"
            class="image-dialog">
            <img :src="previewImageUrl" class="preview-image" alt="图片预览">
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    datasets: [],
                    collections: [],
                    images: [],
                    selectedDataset: '',
                    selectedCollection: '',
                    selectedImages: [],
                    selectAll: false,
                    currentPage: 1,
                    pageSize: 20,
                    previewDialogVisible: false,
                    previewImageUrl: '',
                    dateRange: [], // 日期范围
                    defaultDateRange: [] // 默认日期范围（最近一个月）
                }
            },
            computed: {
                paginatedImages() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.images.slice(start, end);
                }
            },
            mounted() {
                // 设置默认时间范围为最近一个月
                const today = new Date();
                const oneMonthAgo = new Date();
                oneMonthAgo.setMonth(today.getMonth() - 1);

                // 格式化为 YYYY-MM-DD 格式
                const formatDate = (date) => {
                    return date.toISOString().split('T')[0];
                };

                this.defaultDateRange = [formatDate(oneMonthAgo), formatDate(today)];
                this.dateRange = [...this.defaultDateRange];

                this.loadDatasets();
            },
            methods: {
                async loadDatasets() {
                    try {
                        const response = await axios.get('/getDatasList/');
                        if (response.data.code === 200) {
                            this.datasets = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载知识库失败:', error);
                        this.$message.error('加载知识库失败');
                    }
                },
                
                async loadCollections() {
                    if (!this.selectedDataset) return;
                    
                    try {
                        const response = await axios.get(`/getcollectionsList/?datasetId=${this.selectedDataset}`);
                        if (response.data.code === 200) {
                            this.collections = response.data.data;
                            this.selectedCollection = '';
                            this.images = [];
                        }
                    } catch (error) {
                        console.error('加载文档集合失败:', error);
                        this.$message.error('加载文档集合失败');
                    }
                },
                
                async loadImages() {
                    if (!this.selectedCollection) {
                        this.$message.warning('请先选择文档集合');
                        return;
                    }

                    // 确保有日期范围，如果没有则使用默认值
                    const dateStart = this.dateRange[0] || this.defaultDateRange[0];
                    const dateEnd = this.dateRange[1] || this.defaultDateRange[1];

                    if (!dateStart || !dateEnd) {
                        this.$message.warning('请选择查询时间范围');
                        return;
                    }

                    try {
                        this.loading = true;
                        const response = await axios.get(`/getDatasetsDatas_images/?collectionId=${this.selectedCollection}&dateStart=${dateStart}&dateEnd=${dateEnd}`);
                        if (response.data.code === 200) {
                            this.images = response.data.data.data || [];
                            this.currentPage = 1;
                        } else {
                            this.$message.error(response.data.message || '加载图片失败');
                        }
                    } catch (error) {
                        console.error('加载图片失败:', error);
                        this.$message.error('加载图片失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                resetDateRange() {
                    this.dateRange = [...this.defaultDateRange];
                    if (this.selectedCollection) {
                        this.loadImages();
                    }
                },

                handleSelectionChange(selection) {
                    this.selectedImages = selection;
                },
                
                toggleSelectAll() {
                    this.$refs.table.toggleAllSelection();
                },
                
                async updateSingleImage(image) {
                    try {
                        this.$set(image, 'updating', true);
                        const response = await axios.post('/updateSingleImage/', {
                            imageId: image.imageId,
                            documentId: image.documentId
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('更新成功');
                            this.loadImages();
                        } else {
                            this.$message.error(response.data.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新图片失败:', error);
                        this.$message.error('更新图片失败');
                    } finally {
                        this.$set(image, 'updating', false);
                    }
                },
                
                async batchUpdate() {
                    if (!this.selectedImages.length) {
                        this.$message.warning('请先选择要更新的图片');
                        return;
                    }
                    
                    try {
                        this.loading = true;
                        const imageIds = this.selectedImages.map(img => img.imageId);
                        const response = await axios.post('/batchUpdateImages/', {
                            imageIds: imageIds
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success(`批量更新成功，共更新 ${imageIds.length} 张图片`);
                            this.loadImages();
                            this.selectedImages = [];
                        } else {
                            this.$message.error(response.data.message || '批量更新失败');
                        }
                    } catch (error) {
                        console.error('批量更新失败:', error);
                        this.$message.error('批量更新失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                deleteImage(image) {
                    this.$confirm('确定要删除这张图片吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        try {
                            const response = await axios.post('/deleteImage/', {
                                imageId: image.imageId
                            });
                            
                            if (response.data.code === 200) {
                                this.$message.success('删除成功');
                                this.loadImages();
                            } else {
                                this.$message.error(response.data.message || '删除失败');
                            }
                        } catch (error) {
                            console.error('删除图片失败:', error);
                            this.$message.error('删除图片失败');
                        }
                    });
                },
                
                previewImage(imageUrl) {
                    this.previewImageUrl = imageUrl;
                    this.previewDialogVisible = true;
                },
                
                handleImageError(event) {
                    event.target.src = '/static/images/image-error.png';
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '未知';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },
                
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.currentPage = 1;
                },
                
                handleCurrentChange(val) {
                    this.currentPage = val;
                }
            }
        });
    </script>
</body>
</html>
