<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>知识库管理系统 - 首页</title>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <!-- 引入统一样式 -->
  <link rel="stylesheet" href="/static/css/common.css">
  <!-- 引入Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- 引入Element UI组件库 -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <!-- 引入axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  
  <style>
    /* 强制确保Element UI容器正确布局 */
    .el-container {
      display: flex !important;
    }

    .el-container.is-vertical {
      flex-direction: column !important;
    }

    .el-container:not(.is-vertical) {
      flex-direction: row !important;
    }

    /* 使用统一的CSS变量，提供回退值 */
    body {
      margin: 0;
      padding: 0;
      background: var(--background-white, #ffffff);
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      min-height: 100vh;
      color: var(--text-primary, #303133);
    }

    /* 关键布局修复 - 确保左右布局 */
    .main-container {
      height: 100vh !important;
      display: flex !important;
      flex-direction: row !important;
    }

    .el-aside {
      min-width: 240px !important;
      max-width: 240px !important;
      background: #f5f7fa !important;
      flex-shrink: 0 !important;
    }

    .el-main {
      flex: 1 !important;
      padding: 0 !important;
      overflow: hidden !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .nav-menu {
      height: calc(100% - 60px) !important;
      margin-top: 60px;
      border-right: none !important;
      background: var(--background-white, #ffffff) !important;
    }

    .toolbar {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: var(--spacing-lg, 20px);
      padding: var(--spacing-md, 16px) var(--spacing-lg, 20px);
      background: var(--background-white, #ffffff);
      height: 60px;
      box-shadow: var(--shadow-base, 0 2px 12px 0 rgba(0, 0, 0, 0.1));
      position: relative;
      z-index: 1000;
      flex-shrink: 0;
    }

    .home-content {
      padding: var(--spacing-lg, 20px);
      height: calc(100vh - 140px);
      overflow-y: auto;
      background: var(--background-base, #f5f7fa);
      flex: 1;
    }

    .header {
      text-align: center;
      margin-bottom: var(--spacing-xl, 32px);
      background: var(--background-white, #ffffff);
      padding: var(--spacing-xl, 32px) var(--spacing-lg, 20px);
      border-radius: var(--radius-md, 8px);
      box-shadow: var(--shadow-light, 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04));
    }

    .header h1 {
      color: var(--text-primary);
      font-size: var(--font-size-xxl);
      font-weight: 600;
      margin: 0 0 var(--spacing-sm) 0;
    }

    .header p {
      color: var(--text-regular);
      font-size: var(--font-size-md);
      margin: 0;
    }

    .datasets-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-lg, 20px);
      margin-bottom: var(--spacing-lg, 20px);
    }

    .dataset-card {
      background: var(--background-white, #ffffff);
      border-radius: var(--radius-md, 8px);
      padding: var(--spacing-lg, 20px);
      box-shadow: var(--shadow-light, 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04));
      transition: var(--transition-base, all 0.3s ease);
      cursor: pointer;
      position: relative;
      overflow: hidden;
      border: 1px solid var(--border-lighter, #ebeef5);
    }

    .dataset-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-hover, 0 8px 32px rgba(0, 0, 0, 0.1));
      border-color: var(--primary-color, #667eea);
    }

    .dataset-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
    }

    .dataset-header {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-lg);
    }

    .dataset-avatar {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-md);
      background: var(--primary-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: var(--spacing-md);
      color: var(--background-white);
      font-size: var(--font-size-xl);
      font-weight: bold;
    }

    .dataset-info h3 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--font-size-xl);
      font-weight: 600;
      color: var(--text-primary);
      line-height: 1.2;
    }

    .dataset-info p {
      margin: 0;
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      line-height: 1.4;
    }

    .dataset-description {
      color: var(--text-regular);
      font-size: var(--font-size-sm);
      line-height: 1.6;
      margin-bottom: var(--spacing-lg);
      min-height: 40px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .dataset-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-lg);
    }

    .stat-item {
      text-align: center;
      flex: 1;
    }

    .stat-number {
      font-size: var(--font-size-xl);
      font-weight: bold;
      color: var(--primary-color);
      margin-bottom: var(--spacing-xs);
    }

    .stat-label {
      font-size: var(--font-size-xs);
      color: var(--text-secondary);
    }

    .dataset-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--spacing-lg);
      border-top: 1px solid var(--border-extra-light);
    }

    .update-time {
      font-size: var(--font-size-xs);
      color: var(--text-placeholder);
    }

    .enter-btn {
      background: var(--primary-color);
      color: var(--background-white);
      border: none;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-sm);
      font-size: var(--font-size-xs);
      cursor: pointer;
      transition: var(--transition-base);
    }

    .enter-btn:hover {
      background: #66b1ff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-xl) var(--spacing-lg);
      color: var(--text-secondary);
      background: var(--background-white);
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-light);
    }

    .empty-state i {
      font-size: 64px;
      margin-bottom: var(--spacing-lg);
      opacity: 0.6;
      color: var(--text-placeholder);
    }

    .empty-state h3 {
      font-size: var(--font-size-lg);
      margin-bottom: var(--spacing-sm);
      color: var(--text-regular);
    }

    .empty-state p {
      font-size: var(--font-size-sm);
      opacity: 0.8;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .datasets-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (max-width: 900px) {
      .datasets-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: var(--font-size-xxl);
      }

      .datasets-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .dataset-card {
        padding: var(--spacing-md);
      }

      .home-content {
        padding: var(--spacing-md);
      }

      .toolbar {
        padding: var(--spacing-sm) var(--spacing-md);
        height: 50px;
      }

      .nav-menu {
        margin-top: 50px;
        height: calc(100% - 50px);
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: var(--spacing-lg) var(--spacing-md);
      }

      .header h1 {
        font-size: var(--font-size-xl);
      }

      .dataset-card {
        padding: var(--spacing-sm);
      }

      .dataset-header {
        margin-bottom: var(--spacing-md);
      }

      .dataset-avatar {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-md);
      }

      .dataset-info h3 {
        font-size: var(--font-size-md);
      }

      .stat-number {
        font-size: var(--font-size-md);
      }
    }
        height: 50px;
      }

      .nav-menu {
        margin-top: 50px;
        height: calc(100% - 50px);
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: var(--spacing-lg) var(--spacing-md);
      }

      .header h1 {
        font-size: var(--font-size-xl);
      }

      .dataset-card {
        padding: var(--spacing-sm);
      }

      .dataset-header {
        margin-bottom: var(--spacing-md);
      }

      .dataset-avatar {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-md);
      }

      .dataset-info h3 {
        font-size: var(--font-size-md);
      }

      .stat-number {
        font-size: var(--font-size-md);
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <el-container class="main-container" direction="horizontal" style="height: 100vh;">
      <!-- 左侧导航 -->
      <el-aside width="240px" style="background:#f5f7fa; min-width: 240px; flex-shrink: 0;">
        <el-menu
          class="nav-menu"
          :default-active="activeMenu"
          background-color="#f5f7fa"
          text-color="#606266"
          active-text-color="#409EFF"
          @select="handleMenuSelect">
          <el-menu-item index="home">
            <i class="el-icon-s-home"></i>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="docs">
            <i class="el-icon-folder-opened"></i>
            <span>文档库</span>
          </el-menu-item>
          <el-menu-item index="images">
            <i class="el-icon-picture"></i>
            <span>图片管理</span>
          </el-menu-item>
          <el-menu-item index="audit">
            <i class="el-icon-view"></i>
            <span>图片审核</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 右侧内容 -->
      <el-main style="padding:0; overflow: hidden; display: flex; flex-direction: column; flex: 1;">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
          <h2 style="margin: 0; color: #303133;">知识库管理系统</h2>
        </div>

        <!-- 主要内容区域 -->
        <div class="home-content">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-loading-spinner></el-loading-spinner>
          </div>

          <!-- 知识库网格 -->
          <div v-else-if="datasets.length > 0" class="datasets-grid">
        <div 
          v-for="dataset in datasets" 
          :key="dataset.id" 
          class="dataset-card"
          @click="enterDataset(dataset)"
        >
          <div class="dataset-header">
            <div class="dataset-avatar">
              {{ dataset.name.charAt(0) }}
            </div>
            <div class="dataset-info">
              <h3>{{ dataset.name }}</h3>
              <p>{{ formatUpdateTime(dataset.updateTime) }}</p>
            </div>
          </div>
          
          <div class="dataset-description">
            {{ dataset.intro || '暂无描述' }}
          </div>
          
          <div class="dataset-stats">
            <div class="stat-item">
              <div class="stat-number">{{ dataset.docCount }}</div>
              <div class="stat-label">文档总数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ dataset.auditedCount }}</div>
              <div class="stat-label">已审核</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ ((dataset.auditedCount / dataset.docCount) * 100).toFixed(0) || 0 }}%</div>
              <div class="stat-label">审核率</div>
            </div>
          </div>
          
          <div class="dataset-meta">
            <span class="update-time">
              更新于 {{ formatUpdateTime(dataset.updateTime) }}
            </span>
            <button class="enter-btn">
              进入管理
            </button>
          </div>
        </div>
      </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <i class="el-icon-folder-opened"></i>
            <h3>暂无知识库</h3>
            <p>还没有创建任何知识库，请先创建知识库</p>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          loading: true,
          datasets: [],
          activeMenu: 'home'
        }
      },
      mounted() {
        this.loadDatasets();
      },
      methods: {
        async loadDatasets() {
          try {
            this.loading = true;
            const response = await axios.get('/getHomeDatasetsInfo/');
            if (response.data.code === 200) {
              this.datasets = response.data.data;
            } else {
              this.$message.error('获取知识库信息失败');
            }
          } catch (error) {
            console.error('加载知识库失败:', error);
            this.$message.error('网络错误，请稍后重试');
          } finally {
            this.loading = false;
          }
        },
        
        enterDataset(dataset) {
          // 跳转到文档库管理页面
          window.location.href = `/docs/?datasetId=${dataset.id}`;
        },

        handleMenuSelect(index) {
          this.activeMenu = index;
          switch(index) {
            case 'home':
              window.location.href = '/';
              break;
            case 'docs':
              window.location.href = '/docs/';
              break;
            case 'images':
              window.location.href = '/h5_images/';
              break;
            case 'audit':
              window.location.href = '/h5_images_audit_wh/';
              break;
          }
        },
        
        formatUpdateTime(timeStr) {
          if (!timeStr) return '未知';
          const date = new Date(timeStr);
          const now = new Date();
          const diff = now - date;
          
          if (diff < 60000) return '刚刚';
          if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
          if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
          if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
          
          return date.toLocaleDateString('zh-CN');
        }
      }
    });
  </script>
</body>
</html>
